<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clean Sweep ✨ - Girly Sorting Game</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #f0e6ff 50%, #e6f3ff 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }
        
        /* Sparkle animation background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(2px 2px at 20px 30px, #fff, transparent),
                radial-gradient(2px 2px at 40px 70px, #ffb3d9, transparent),
                radial-gradient(1px 1px at 90px 40px, #d9b3ff, transparent),
                radial-gradient(1px 1px at 130px 80px, #b3d9ff, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: sparkle 3s linear infinite;
            pointer-events: none;
            z-index: -1;
        }
        
        @keyframes sparkle {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-100px); }
        }
        
        .game-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }
        
        .screen {
            display: none;
            text-align: center;
        }
        
        .screen.active {
            display: block;
        }
        
        .title {
            font-size: 3rem;
            font-weight: 700;
            color: #ff69b4;
            text-shadow: 2px 2px 4px rgba(255, 105, 180, 0.3);
            margin-bottom: 20px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #8a2be2;
            margin-bottom: 30px;
        }
        
        .btn {
            background: linear-gradient(45deg, #ff69b4, #ff1493);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 25px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(255, 105, 180, 0.4);
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 105, 180, 0.6);
        }
        
        .game-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.8);
            padding: 15px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        
        .timer, .score, .streak {
            font-size: 1.2rem;
            font-weight: 600;
            color: #8a2be2;
        }
        
        .game-area {
            display: flex;
            flex-direction: column;
            gap: 30px;
            margin-bottom: 20px;
        }

        .bins-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            padding: 0 20px;
        }

        .bin {
            height: 120px;
            border-radius: 15px 15px 5px 5px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-end;
            font-size: 1rem;
            font-weight: 600;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            box-shadow:
                0 4px 15px rgba(0,0,0,0.2),
                inset 0 -10px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            padding-bottom: 15px;
        }

        .bin::before {
            content: '';
            position: absolute;
            top: 0;
            left: 10%;
            right: 10%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 10px 10px 0 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .bin::after {
            content: '';
            position: absolute;
            top: 8px;
            left: 5%;
            right: 5%;
            height: 4px;
            background: rgba(255,255,255,0.2);
            border-radius: 5px;
        }
        
        .bin.makeup { background: linear-gradient(135deg, #ff69b4, #ff1493); }
        .bin.fashion { background: linear-gradient(135deg, #dda0dd, #9370db); }
        .bin.school { background: linear-gradient(135deg, #87ceeb, #4682b4); }
        .bin.trash { background: linear-gradient(135deg, #c0c0c0, #808080); }
        
        .bin.drag-over {
            transform: scale(1.05);
            box-shadow: 0 6px 25px rgba(0,0,0,0.3);
        }
        
        .bin-icon {
            font-size: 2rem;
            margin-bottom: 5px;
        }
        
        .objects-area {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 30px;
            min-height: 250px;
            position: relative;
            backdrop-filter: blur(10px);
            border: 3px dashed rgba(255, 105, 180, 0.3);
            margin-bottom: 10px;
        }

        .objects-area::before {
            content: '✨ Drag items to the correct bins below! ✨';
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.9rem;
            color: #8a2be2;
            font-weight: 600;
        }
        
        .object {
            position: absolute;
            font-size: 2.5rem;
            cursor: grab;
            user-select: none;
            transition: all 0.3s ease;
            z-index: 10;
            filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.2));
        }
        
        .object:hover {
            transform: scale(1.1);
        }
        
        .object.dragging {
            cursor: grabbing;
            transform: scale(1.2);
            z-index: 100;
        }
        
        .final-score {
            font-size: 2rem;
            color: #ff69b4;
            margin: 20px 0;
        }
        
        .score-title {
            font-size: 1.5rem;
            color: #8a2be2;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .title { font-size: 2rem; }
            .bins-container {
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }
            .bin {
                height: 100px;
                font-size: 0.9rem;
            }
            .object { font-size: 2rem; }
            .objects-area {
                min-height: 200px;
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .bins-container {
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }
            .bin {
                height: 80px;
                font-size: 0.8rem;
            }
            .bin-icon { font-size: 1.5rem; }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- Start Screen -->
        <div id="startScreen" class="screen active">
            <h1 class="title">Clean Sweep ✨</h1>
            <p class="subtitle">Sort the messy objects into the right bins before time runs out!</p>
            <div style="margin: 30px 0; font-size: 1rem; color: #8a2be2;">
                <p>🎯 Correct sort: +5 points | ❌ Wrong sort: -2 points</p>
                <p>🔥 10 in a row bonus: +20 points | ⏰ 2 minutes to play</p>
            </div>
            <button class="btn" onclick="startGame()">Start Playing! 💖</button>
        </div>
        
        <!-- Game Screen -->
        <div id="gameScreen" class="screen">
            <div class="game-info">
                <div class="timer">⏰ Time: <span id="timeLeft">2:00</span></div>
                <div class="score">💎 Score: <span id="currentScore">0</span></div>
                <div class="streak">🔥 Streak: <span id="currentStreak">0</span></div>
            </div>
            
            <div class="game-area">
                <div class="objects-area" id="objectsArea">
                    <!-- Objects will be spawned here -->
                </div>

                <div class="bins-container">
                    <div class="bin makeup" data-category="makeup">
                        <div class="bin-icon">💄</div>
                        <div>Makeup</div>
                    </div>
                    <div class="bin fashion" data-category="fashion">
                        <div class="bin-icon">👠</div>
                        <div>Fashion</div>
                    </div>
                    <div class="bin school" data-category="school">
                        <div class="bin-icon">📚</div>
                        <div>School</div>
                    </div>
                    <div class="bin trash" data-category="trash">
                        <div class="bin-icon">🗑️</div>
                        <div>Trash</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- End Screen -->
        <div id="endScreen" class="screen">
            <h1 class="title">Game Over! ✨</h1>
            <div class="final-score">Final Score: <span id="finalScore">0</span></div>
            <div class="score-title" id="scoreTitle">Amazing Job! 👑</div>
            <button class="btn" onclick="restartGame()">Play Again! 💖</button>
        </div>
    </div>

    <script>
        // Game state
        let gameState = {
            score: 0,
            streak: 0,
            timeLeft: 120, // 2 minutes in seconds
            gameTimer: null,
            spawnTimer: null,
            isPlaying: false,
            objects: []
        };
        
        // Game objects with their categories and sounds
        const gameObjects = {
            makeup: [
                { emoji: '💄', sound: 'pop' },
                { emoji: '💋', sound: 'pop' },
                { emoji: '✨', sound: 'pop' },
                { emoji: '💅', sound: 'pop' }
            ],
            fashion: [
                { emoji: '👠', sound: 'click' },
                { emoji: '👗', sound: 'click' },
                { emoji: '👜', sound: 'click' },
                { emoji: '💍', sound: 'click' }
            ],
            school: [
                { emoji: '📚', sound: 'crinkle' },
                { emoji: '✏️', sound: 'crinkle' },
                { emoji: '📝', sound: 'crinkle' },
                { emoji: '🎒', sound: 'crinkle' }
            ],
            trash: [
                { emoji: '🗑️', sound: 'swoosh' },
                { emoji: '🥤', sound: 'swoosh' },
                { emoji: '🍕', sound: 'swoosh' },
                { emoji: '📰', sound: 'swoosh' }
            ]
        };

        // Audio context for ASMR sounds
        let audioContext;

        function initAudio() {
            if (!audioContext) {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
            }
        }

        function playSound(soundType) {
            if (!audioContext) return;

            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            switch(soundType) {
                case 'pop':
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
                    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.1);
                    break;
                case 'click':
                    oscillator.frequency.setValueAtTime(1200, audioContext.currentTime);
                    gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.05);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.05);
                    break;
                case 'crinkle':
                    // White noise simulation
                    const bufferSize = audioContext.sampleRate * 0.2;
                    const buffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
                    const data = buffer.getChannelData(0);
                    for (let i = 0; i < bufferSize; i++) {
                        data[i] = Math.random() * 2 - 1;
                    }
                    const noise = audioContext.createBufferSource();
                    noise.buffer = buffer;
                    const filter = audioContext.createBiquadFilter();
                    filter.type = 'highpass';
                    filter.frequency.value = 1000;
                    noise.connect(filter);
                    filter.connect(gainNode);
                    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
                    noise.start();
                    noise.stop(audioContext.currentTime + 0.2);
                    return;
                case 'swoosh':
                    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.3);
                    gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
                    oscillator.start();
                    oscillator.stop(audioContext.currentTime + 0.3);
                    break;
            }
        }

        function startGame() {
            initAudio();
            gameState.score = 0;
            gameState.streak = 0;
            gameState.timeLeft = 120;
            gameState.isPlaying = true;
            gameState.objects = [];

            document.getElementById('startScreen').classList.remove('active');
            document.getElementById('gameScreen').classList.add('active');

            updateDisplay();
            startTimer();
            spawnObject();

            // Spawn objects every 2-4 seconds
            gameState.spawnTimer = setInterval(() => {
                if (gameState.isPlaying) {
                    spawnObject();
                }
            }, Math.random() * 2000 + 2000);
        }

        function startTimer() {
            gameState.gameTimer = setInterval(() => {
                gameState.timeLeft--;
                updateDisplay();

                if (gameState.timeLeft <= 0) {
                    endGame();
                }
            }, 1000);
        }

        function updateDisplay() {
            const minutes = Math.floor(gameState.timeLeft / 60);
            const seconds = gameState.timeLeft % 60;
            document.getElementById('timeLeft').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('currentScore').textContent = gameState.score;
            document.getElementById('currentStreak').textContent = gameState.streak;
        }

        function spawnObject() {
            const categories = Object.keys(gameObjects);
            const randomCategory = categories[Math.floor(Math.random() * categories.length)];
            const randomObject = gameObjects[randomCategory][Math.floor(Math.random() * gameObjects[randomCategory].length)];

            const objectElement = document.createElement('div');
            objectElement.className = 'object';
            objectElement.textContent = randomObject.emoji;
            objectElement.dataset.category = randomCategory;
            objectElement.dataset.sound = randomObject.sound;

            // Random position within objects area
            const objectsArea = document.getElementById('objectsArea');
            const maxX = objectsArea.clientWidth - 60;
            const maxY = objectsArea.clientHeight - 60;

            objectElement.style.left = Math.random() * maxX + 'px';
            objectElement.style.top = Math.random() * maxY + 'px';

            // Add drag functionality
            makeDraggable(objectElement);

            objectsArea.appendChild(objectElement);
            gameState.objects.push(objectElement);

            // Remove old objects if too many
            if (gameState.objects.length > 8) {
                const oldObject = gameState.objects.shift();
                if (oldObject.parentNode) {
                    oldObject.parentNode.removeChild(oldObject);
                }
            }
        }

        function makeDraggable(element) {
            let isDragging = false;
            let startX, startY, initialX, initialY;

            // Mouse events
            element.addEventListener('mousedown', dragStart);
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', dragEnd);

            // Touch events for mobile
            element.addEventListener('touchstart', dragStart, { passive: false });
            document.addEventListener('touchmove', drag, { passive: false });
            document.addEventListener('touchend', dragEnd);

            function dragStart(e) {
                e.preventDefault();
                isDragging = true;
                element.classList.add('dragging');

                const clientX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
                const clientY = e.type === 'touchstart' ? e.touches[0].clientY : e.clientY;

                startX = clientX;
                startY = clientY;
                initialX = element.offsetLeft;
                initialY = element.offsetTop;
            }

            function drag(e) {
                if (!isDragging) return;
                e.preventDefault();

                const clientX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
                const clientY = e.type === 'touchmove' ? e.touches[0].clientY : e.clientY;

                const deltaX = clientX - startX;
                const deltaY = clientY - startY;

                element.style.left = (initialX + deltaX) + 'px';
                element.style.top = (initialY + deltaY) + 'px';

                // Check for bin hover
                const bins = document.querySelectorAll('.bin');
                bins.forEach(bin => bin.classList.remove('drag-over'));

                const elementBelow = document.elementFromPoint(clientX, clientY);
                const bin = elementBelow?.closest('.bin');
                if (bin) {
                    bin.classList.add('drag-over');
                }
            }

            function dragEnd(e) {
                if (!isDragging) return;
                isDragging = false;
                element.classList.remove('dragging');

                const clientX = e.type === 'touchend' ? e.changedTouches[0].clientX : e.clientX;
                const clientY = e.type === 'touchend' ? e.changedTouches[0].clientY : e.clientY;

                // Check if dropped on a bin
                const elementBelow = document.elementFromPoint(clientX, clientY);
                const bin = elementBelow?.closest('.bin');

                document.querySelectorAll('.bin').forEach(b => b.classList.remove('drag-over'));

                if (bin) {
                    const binCategory = bin.dataset.category;
                    const objectCategory = element.dataset.category;
                    const soundType = element.dataset.sound;

                    if (binCategory === objectCategory) {
                        // Correct sort
                        gameState.score += 5;
                        gameState.streak++;

                        // Bonus for 10 in a row
                        if (gameState.streak === 10) {
                            gameState.score += 20;
                            gameState.streak = 0; // Reset streak after bonus
                        }

                        playSound(soundType);
                        element.remove();

                        // Remove from objects array
                        const index = gameState.objects.indexOf(element);
                        if (index > -1) {
                            gameState.objects.splice(index, 1);
                        }
                    } else {
                        // Wrong sort
                        gameState.score -= 2;
                        gameState.streak = 0; // Reset streak

                        // Return object to original area
                        const objectsArea = document.getElementById('objectsArea');
                        const maxX = objectsArea.clientWidth - 60;
                        const maxY = objectsArea.clientHeight - 60;
                        element.style.left = Math.random() * maxX + 'px';
                        element.style.top = Math.random() * maxY + 'px';
                    }

                    updateDisplay();
                }
            }
        }

        function endGame() {
            gameState.isPlaying = false;
            clearInterval(gameState.gameTimer);
            clearInterval(gameState.spawnTimer);

            // Clear all objects
            gameState.objects.forEach(obj => {
                if (obj.parentNode) {
                    obj.parentNode.removeChild(obj);
                }
            });
            gameState.objects = [];

            // Show end screen
            document.getElementById('gameScreen').classList.remove('active');
            document.getElementById('endScreen').classList.add('active');

            // Set final score and title
            document.getElementById('finalScore').textContent = gameState.score;

            let title = '';
            if (gameState.score >= 100) {
                title = 'Organized Queen 👑';
            } else if (gameState.score >= 50) {
                title = 'Pretty Good Babe 💖';
            } else if (gameState.score >= 20) {
                title = 'Getting There Cutie 🌸';
            } else {
                title = 'Messy Babe 💔';
            }

            document.getElementById('scoreTitle').textContent = title;
        }

        function restartGame() {
            document.getElementById('endScreen').classList.remove('active');
            document.getElementById('startScreen').classList.add('active');
        }

        // Initialize drag and drop for bins
        document.addEventListener('DOMContentLoaded', function() {
            const bins = document.querySelectorAll('.bin');
            bins.forEach(bin => {
                bin.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('drag-over');
                });

                bin.addEventListener('dragleave', function(e) {
                    this.classList.remove('drag-over');
                });

                bin.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('drag-over');
                });
            });
        });
    </script>
</body>
</html>
